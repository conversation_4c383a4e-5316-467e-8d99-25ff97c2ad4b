"use client"

import type React from "react"

import { useState, useEffect, useRef, use<PERSON><PERSON>back, useMemo } from "react"
import { useSearch<PERSON>ara<PERSON>, useRouter } from "next/navigation"
import { But<PERSON> } from "components/ui/button"
import { Input } from "components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "components/ui/select"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "components/ui/accordion"
import { Checkbox } from "components/ui/checkbox"
import { Slider } from "components/ui/slider"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import PromptCardComponent from "components/prompt-card" // Renamed to avoid conflict with type
import PromptListItemComponent from "components/prompt-list-item" // Renamed to avoid conflict with type
import { PromptCard } from "@/lib/types"
import PromptGrid from "components/prompt-grid"
import CategoryFilters from "components/category-filters"
import { Loader2, SearchIcon, X, SlidersHorizontal, Grid3X3, List, ChevronDown, HelpCircle, Filter } from "lucide-react"
import { allCategories } from "lib/data/categories"
import { allTags } from "lib/data/tags"
import { allTools } from "lib/data/tools"
import { useFilteredPrompts } from "@/hooks/use-filtered-prompts"
import { useUser } from "@/lib/hooks/use-user"
import { cn } from "@/lib/utils" // Assuming cn is available for Badge component


export default function SearchPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const { user } = useUser() // ADD: Get current user

  // --- State Initialization ---
  const initialQuery = searchParams.get("q") || ""
  const initialSortBy = (searchParams.get("sort") as "trending" | "top" | "latest") || "trending"
  const initialTimePeriod = (searchParams.get("time") as "week" | "month" | "all") || "all"
  const initialView = (searchParams.get("view") as "grid" | "list") || "grid"
  const initialCategories = searchParams.get("categories")?.split(",").filter(Boolean) || []
  const initialTools = searchParams.get("tools")?.split(",").filter(Boolean) || []
  const initialTags = searchParams.get("tags")?.split(",").filter(Boolean) || []
  const initialModels = searchParams.get("models")?.split(",").filter(Boolean) || []
  const initialDifficultyStr = searchParams.get("difficulty");
  const initialWordCountStr = searchParams.get("words");
  const initialPremium = searchParams.get("premium") === "true"

  const parseRange = (str: string | null, defaultRange: [number, number]): [number, number] => {
    if (!str) return defaultRange;
    const parts = str.split("-").map(Number);
    if (parts.length === 2 && !isNaN(parts[0]) && !isNaN(parts[1])) {
      return [parts[0], parts[1]];
    }
    return defaultRange;
  };

  const initialDifficulty = parseRange(initialDifficultyStr, [0, 5]); // Default min to 0
  const initialWordCount = parseRange(initialWordCountStr, [0, 1000]);


  // State for search input (updates immediately)
  const [searchInput, setSearchInput] = useState(initialQuery)
  // State for debounced search query (used for fetching/filtering)
  const [debouncedQuery, setDebouncedQuery] = useState(initialQuery)

  // State for filters
  const [sortBy, setSortBy] = useState(initialSortBy)
  const [timePeriod, setTimePeriod] = useState(initialTimePeriod)
  const [viewMode, setViewMode] = useState(initialView)

  // Progressive disclosure state
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const [showFiltersHelp, setShowFiltersHelp] = useState(false)

  // Filter state managed within the page (passed to CategoryFilters and useFilteredPrompts)
  const [filterSearchTerm, setFilterSearchTerm] = useState<string>("") // Search within filters
  // Advanced filters state
  const [selectedCategories, setSelectedCategories] = useState<string[]>(initialCategories)
  const [selectedTools, setSelectedTools] = useState<string[]>(initialTools)
  const [selectedTags, setSelectedTags] = useState<string[]>(initialTags)
  const [selectedModels, setSelectedModels] = useState<string[]>(initialModels)

  // Auto-expand advanced filters if any advanced filters are active
  useEffect(() => {
    const hasAdvancedFilters = selectedCategories.length > 0 ||
                              selectedTools.length > 0 ||
                              selectedTags.length > 0 ||
                              selectedModels.length > 0
    if (hasAdvancedFilters && !showAdvancedFilters) {
      setShowAdvancedFilters(true)
    }
  }, [selectedCategories, selectedTools, selectedTags, selectedModels, showAdvancedFilters])

  // Client-side filter state (not passed to API hook)


  // Convert frontend sort values to API sort values
  const getApiSortParams = () => {
    switch (sortBy) {
      case "trending":
        return { sortBy: "trending_score", sortOrder: "desc" as const }
      case "top":
        return { sortBy: "rating", sortOrder: "desc" as const }
      case "latest":
        return { sortBy: "created_at", sortOrder: "desc" as const }
      default:
        return { sortBy: "created_at", sortOrder: "desc" as const }
    }
  }

  const { sortBy: apiSortBy, sortOrder: apiSortOrder } = getApiSortParams()

  // ---- Fetch Prompts Hook ----
  const {
    prompts: rawPrompts, // Raw prompts from API based on supported filters
    isLoading, // Loading state from the hook
    error, // Error state from the hook
    hasMore, // Whether more prompts can be loaded
    loadMore, // Function to load more prompts
  } = useFilteredPrompts({
    categorySlugs: selectedCategories,
    toolSlugs: selectedTools,
    tagSlugs: selectedTags,
    aiModelSlugs: selectedModels,
    searchQuery: debouncedQuery, // Use debounced MAIN search query
    currentUserId: user?.id, // NEW: Pass current user ID
    sortBy: apiSortBy,
    sortOrder: apiSortOrder,
  });

  // Use raw prompts directly since CollectionsDropdown handles all state management
  const prompts = rawPrompts;




  // Ref to prevent effect runs on mount triggered by state init sync
  const isMounted = useRef(false);

  // --- Client-side Filtering & Sorting ---

  // Function to filter prompts by time period
  const filterPromptsByTime = (promptsToFilter: PromptCard[], timePeriod: string): PromptCard[] => {
    if (!promptsToFilter) return []; // Handle null/undefined case
    const now = new Date();
    return promptsToFilter.filter((prompt) => {
      // Handle both camelCase and snake_case property names
      const createdAt = prompt.createdAt || (prompt as any).created_at;
      if (!createdAt) return timePeriod === 'all'; // Handle missing createdAt
      const promptDate = new Date(createdAt);
      if (isNaN(promptDate.getTime())) return timePeriod === 'all'; // Handle invalid date

      if (timePeriod === 'week') {
        const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000); // Fixed date calculation
        return promptDate >= oneWeekAgo;
      } else if (timePeriod === 'month') {
        const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000); // Fixed date calculation
        return promptDate >= oneMonthAgo;
      }
      return true; // 'all'
    });
  };




  // --- Effects ---

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => setDebouncedQuery(searchInput), 500);
    return () => clearTimeout(timer);
  }, [searchInput]);


  // Function to build the canonical search params string based on current state
  const buildSearchParams = useCallback(() => {
    const params = new URLSearchParams()
    if (debouncedQuery) params.set("q", debouncedQuery)
    if (sortBy !== "trending") params.set("sort", sortBy)
    if (timePeriod !== "all") params.set("time", timePeriod)
    if (viewMode !== "grid") params.set("view", viewMode)
    if (selectedCategories.length > 0) params.set("categories", selectedCategories.join(",")) // Already filters by slug
    if (selectedTools.length > 0) params.set("tools", selectedTools.join(",")) // Already filters by slug
    if (selectedTags.length > 0) params.set("tags", selectedTags.join(",")) // Already filters by slug
    if (selectedModels.length > 0) params.set("models", selectedModels.join(",")) // Already filters by slug
    
    return params.toString()
  }, [debouncedQuery, sortBy, timePeriod, viewMode, selectedCategories, selectedTools, selectedTags, selectedModels]);


  // Effect to synchronize state with URL and trigger data reload *only when filters change*
  // Split into two effects: one for data-affecting changes, one for view-only changes
  useEffect(() => {
    if (!isMounted.current) {
      isMounted.current = true;
      // console.log("SearchPage mounted, loading initial data.");
      return;
    }

    const newSearchString = buildSearchParams();
    const currentSearchString = searchParams.toString();

    if (newSearchString !== currentSearchString) {
        console.log(`Search Params changed: \n Old: ${currentSearchString}\n New: ${newSearchString}. Replacing URL.`);
        router.replace(`/search?${newSearchString}`, { scroll: false });
    }
  }, [
    debouncedQuery,
    sortBy,
    timePeriod,
    selectedCategories,
    selectedTools,
    selectedTags,
    selectedModels,
    buildSearchParams,
    router,
    // Removed viewMode from dependencies to prevent refetching when switching views
  ]);

  // Separate effect for view mode changes (URL update only, no data refetch)
  useEffect(() => {
    if (!isMounted.current) return;

    const newSearchString = buildSearchParams();
    const currentSearchString = searchParams.toString();

    // Only update URL if the difference is just the view mode
    if (newSearchString !== currentSearchString) {
      const currentParams = new URLSearchParams(currentSearchString);
      const newParams = new URLSearchParams(newSearchString);

      // Check if only view mode changed
      currentParams.delete('view');
      newParams.delete('view');

      if (currentParams.toString() === newParams.toString()) {
        // Only view mode changed, update URL without triggering data refetch
        router.replace(`/search?${newSearchString}`, { scroll: false });
      }
    }
  }, [viewMode, buildSearchParams, router, searchParams]);

  // NEW: Effect to listen for URL parameter changes and update state accordingly
  // This handles cases where the user navigates to the search page with new parameters
  // (e.g., from the header search bar)
  useEffect(() => {
    if (!isMounted.current) return; // Skip on initial mount as state is already initialized

    const urlQuery = searchParams.get("q") || ""
    const urlSortBy = (searchParams.get("sort") as "trending" | "top" | "latest") || "trending"
    const urlTimePeriod = (searchParams.get("time") as "week" | "month" | "all") || "all"
    const urlView = (searchParams.get("view") as "grid" | "list") || "grid"
    const urlCategories = searchParams.get("categories")?.split(",").filter(Boolean) || []
    const urlTools = searchParams.get("tools")?.split(",").filter(Boolean) || []
    const urlTags = searchParams.get("tags")?.split(",").filter(Boolean) || []
    const urlModels = searchParams.get("models")?.split(",").filter(Boolean) || []

    // Update state if URL parameters differ from current state
    if (urlQuery !== debouncedQuery) {
      setSearchInput(urlQuery)
      setDebouncedQuery(urlQuery)
    }
    if (urlSortBy !== sortBy) {
      setSortBy(urlSortBy)
    }
    if (urlTimePeriod !== timePeriod) {
      setTimePeriod(urlTimePeriod)
    }
    if (urlView !== viewMode) {
      setViewMode(urlView)
    }
    if (JSON.stringify(urlCategories) !== JSON.stringify(selectedCategories)) {
      setSelectedCategories(urlCategories)
    }
    if (JSON.stringify(urlTools) !== JSON.stringify(selectedTools)) {
      setSelectedTools(urlTools)
    }
    if (JSON.stringify(urlTags) !== JSON.stringify(selectedTags)) {
      setSelectedTags(urlTags)
    }
    if (JSON.stringify(urlModels) !== JSON.stringify(selectedModels)) {
      setSelectedModels(urlModels)
    }
  }, [searchParams]) // Only depend on searchParams to listen for URL changes

  // Effect to update displayed prompts when filters change or new data is loaded
  // This effect is no longer needed as applyFiltersAndSort is called directly with prompts from the hook
  // useEffect(() => {
  //    console.log("Applying filters and sort to loaded data.");
  //    applyFiltersAndSort(allLoadedPrompts);
  // }, [allLoadedPrompts, debouncedQuery, selectedCategories, selectedTools, selectedTags, difficultyRange, wordCountRange, premiumOnly, timePeriod, sortBy, applyFiltersAndSort]);


  // Apply time period filter (since API doesn't handle this)
  const finalDisplayedPrompts = useMemo(() => {
    if (!prompts || prompts.length === 0) return [];
    return filterPromptsByTime(prompts, timePeriod);
  }, [prompts, timePeriod]);

  // Debug logging (can be removed in production)
  if (process.env.NODE_ENV === 'development') {
    console.log("[SearchPage] Debug info:", {
      rawPromptsLength: rawPrompts.length,
      promptsLength: prompts.length,
      finalDisplayedPromptsLength: finalDisplayedPrompts.length,
      isLoading,
      error: error?.message,
      hasMore,
      sortBy,
      timePeriod,
      apiSortBy,
      apiSortOrder,
      selectedCategories,
      selectedTools,
      selectedTags,
      debouncedQuery
    });
  }


  // --- Infinite Scroll ---
  const observerRef = useRef<IntersectionObserver | null>(null)
  const lastPromptRef = useCallback(
    (node: HTMLDivElement | null) => {
      if (isLoading) return
      if (observerRef.current) observerRef.current.disconnect()

      observerRef.current = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting && hasMore) {
           console.log("Intersection observer triggered loadMore");
           loadMore(); // Use the loadMore function from the hook
        }
      })

      if (node) observerRef.current.observe(node)
    },
    [isLoading, hasMore, loadMore], // Depend on loadMore
  )

  // --- Event Handlers ---
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // No need to manually set debounced query, the effect handles it.
    // This function might just ensure the form doesn't fully reload the page.
  }

  const clearAllFilters = () => {
      setSearchInput("")
      setDebouncedQuery("") // Setting debounced query immediately to trigger effect faster
      setSortBy("trending")
      setTimePeriod("all")
      setSelectedCategories([])
      setSelectedTools([])
      setSelectedModels([])
      setSelectedTags([])
      // setDifficultyRange([0, 5]) // Reset to 0-5
      // setWordCountRange([0, 1000])
      // setPremiumOnly(false)
      // Effect will handle URL update and data reload
  };

  // Removed toggleCategory, toggleTool, toggleTag as CategoryFilters handles this


  // --- UI Logic ---
  // const topTags = allTags.slice(0, 20) // No longer needed as CategoryFilters handles tags

  // const hasActiveFilters =
  //   selectedCategories.length > 0 ||
  //   selectedTools.length > 0 ||
  //   selectedTags.length > 0 ||
  //   // difficultyRange[0] !== 0 || // Updated default check
  //   // difficultyRange[1] !== 5 ||
  //   // wordCountRange[0] !== 0 ||
  //   // wordCountRange[1] !== 1000 ||
  //   // premiumOnly ||
  //   debouncedQuery !== "";

  // const hasNonSearchFilters =
  //   selectedCategories.length > 0 ||
  //   selectedTools.length > 0 ||
  //   selectedTags.length > 0 ||
  //   // difficultyRange[0] !== 0 || // Updated default check
  //   // difficultyRange[1] !== 5 ||
  //   // wordCountRange[0] !== 0 ||
  //   // wordCountRange[1] !== 1000 ||
  //   // premiumOnly;

  return (
    <TooltipProvider>
      <div className="container mx-auto px-4 py-8">
        {/* Page Title with Enhanced Visual Hierarchy */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold tracking-tight mb-3">Search AI Prompts</h1>
          <p className="text-lg text-muted-foreground max-w-2xl">
            Discover the perfect AI prompt for your needs. Use filters to narrow down results or browse by category.
          </p>
        </div>

        {/* Main Layout Grid */}
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-4">

          {/* Enhanced Filters Sidebar (Left Column) */}
          <aside className="lg:col-span-1">
            <div className="sticky top-20 space-y-6">

              {/* Primary Search Card */}
              <div className="rounded-xl border bg-card p-6 shadow-sm">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <SearchIcon className="h-5 w-5 text-accent-green" />
                    <h2 className="text-lg font-semibold">Search</h2>
                  </div>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <HelpCircle className="h-4 w-4 text-muted-foreground" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="left" className="max-w-xs">
                      <p>Search through thousands of AI prompts by keywords, topics, or specific use cases.</p>
                    </TooltipContent>
                  </Tooltip>
                </div>

                {/* Enhanced Search Input */}
                <form onSubmit={handleSearchSubmit} className="relative mb-6">
                  <SearchIcon className="absolute left-4 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="e.g., 'marketing copy', 'code review', 'creative writing'..."
                    className="pl-12 pr-10 h-12 text-base rounded-lg border-2 focus:border-accent-green transition-colors"
                    value={searchInput}
                    onChange={(e) => setSearchInput(e.target.value)}
                    aria-label="Search all prompts"
                  />
                  {searchInput && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 hover:bg-muted"
                      onClick={() => setSearchInput("")}
                    >
                      <X className="h-4 w-4" />
                      <span className="sr-only">Clear search</span>
                    </Button>
                  )}
                </form>

                {/* Sort and Time Controls with Better Spacing */}
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground mb-2 block">Sort by</label>
                    <Select value={sortBy} onValueChange={(value)=>setSortBy(value as any)}>
                      <SelectTrigger className="w-full h-10 rounded-lg">
                        <SelectValue placeholder="Sort by"/>
                      </SelectTrigger>
                      <SelectContent className="rounded-lg">
                        <SelectItem value="trending">🔥 Trending</SelectItem>
                        <SelectItem value="top">⭐ Top Rated</SelectItem>
                        <SelectItem value="latest">🆕 Newest</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-muted-foreground mb-2 block">Time period</label>
                    <Select value={timePeriod} onValueChange={(value)=>setTimePeriod(value as any)}>
                      <SelectTrigger className="w-full h-10 rounded-lg">
                        <SelectValue placeholder="Time period"/>
                      </SelectTrigger>
                      <SelectContent className="rounded-lg">
                        <SelectItem value="week">This Week</SelectItem>
                        <SelectItem value="month">This Month</SelectItem>
                        <SelectItem value="all">All Time</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>


              {/* Progressive Disclosure for Advanced Filters */}
              <div className="rounded-xl border bg-card shadow-sm overflow-hidden">
                <Collapsible open={showAdvancedFilters} onOpenChange={setShowAdvancedFilters}>
                  {/* Fixed Header */}
                  <div className="flex items-center justify-between p-6 border-b">
                    <div className="flex items-center gap-2">
                      <Filter className="h-5 w-5 text-accent-green" />
                      <span className="text-lg font-semibold">Advanced Filters</span>
                      {(selectedCategories.length + selectedTools.length + selectedTags.length + selectedModels.length) > 0 && (
                        <span className="ml-2 bg-accent-green text-white text-xs px-2 py-1 rounded-full">
                          {selectedCategories.length + selectedTools.length + selectedTags.length + selectedModels.length}
                        </span>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <HelpCircle className="h-4 w-4 text-muted-foreground" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent side="left" className="max-w-xs">
                          <p>Filter by categories, tools, tags, and AI models to find exactly what you need.</p>
                        </TooltipContent>
                      </Tooltip>
                      <CollapsibleTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <ChevronDown className={`h-4 w-4 text-muted-foreground transition-transform ${showAdvancedFilters ? 'rotate-180' : ''}`} />
                        </Button>
                      </CollapsibleTrigger>
                    </div>
                  </div>

                  {/* Collapsible Content */}
                  <CollapsibleContent>
                    <div className="p-6 pt-4 space-y-4">
                      {/* Clear All Filters Button */}
                      {(selectedCategories.length + selectedTools.length + selectedTags.length + selectedModels.length) > 0 && (
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-muted-foreground">
                            {selectedCategories.length + selectedTools.length + selectedTags.length + selectedModels.length} filters active
                          </span>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={clearAllFilters}
                            className="text-xs hover:bg-destructive hover:text-destructive-foreground"
                          >
                            Clear All
                          </Button>
                        </div>
                      )}

                      {/* Filter Container with Proper Constraints */}
                      <div className="w-full max-h-80 overflow-y-auto overflow-x-hidden">
                        <div className="pr-2"> {/* Add padding for scrollbar */}
                          <CategoryFilters
                            selectedCategories={selectedCategories}
                            onSelectCategories={setSelectedCategories}
                            selectedTags={selectedTags}
                            onSelectTags={setSelectedTags}
                            selectedTools={selectedTools}
                            onSelectTools={setSelectedTools}
                            selectedModels={selectedModels}
                            onSelectModels={setSelectedModels}
                            searchTerm={filterSearchTerm}
                            onSearchChange={setFilterSearchTerm}
                            onClearAll={clearAllFilters}
                            showClearButton={false}
                          />
                        </div>
                      </div>
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              </div>
            </div>
          </aside>

          {/* Enhanced Results Area (Right Column) */}
          <main className="lg:col-span-3 w-full min-w-0">
            {/* Enhanced Top Controls with Consistent Layout */}
            <div className="mb-6 space-y-4 w-full">
              <div className="flex flex-wrap items-center justify-between gap-4 w-full">
                {/* Left Controls: View Mode Toggle */}
                <div className="flex items-center gap-4">
                  <div className="flex items-center rounded-lg border bg-muted/30 p-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      className={`h-8 px-3 rounded-md transition-all ${
                        viewMode === "grid"
                          ? "bg-background shadow-sm text-foreground"
                          : "text-muted-foreground hover:text-foreground"
                      }`}
                      onClick={()=>setViewMode("grid")}
                    >
                      <Grid3X3 className="h-4 w-4 mr-1" />
                      Grid
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className={`h-8 px-3 rounded-md transition-all ${
                        viewMode === "list"
                          ? "bg-background shadow-sm text-foreground"
                          : "text-muted-foreground hover:text-foreground"
                      }`}
                      onClick={()=>setViewMode("list")}
                    >
                      <List className="h-4 w-4 mr-1" />
                      List
                    </Button>
                  </div>
                </div>

                {/* Right Controls: Result Count with Better Styling */}
                <div className="flex items-center gap-2">
                  {(!isLoading || finalDisplayedPrompts.length > 0) && (
                    <div className="text-sm font-medium text-muted-foreground bg-muted/50 px-3 py-1.5 rounded-lg">
                      {finalDisplayedPrompts.length} {finalDisplayedPrompts.length === 1 ? "result" : "results"}
                    </div>
                  )}
                </div>
              </div>

              {/* Enhanced Active Filter Badges - Always Reserve Space */}
              <div className="min-h-[2rem] w-full"> {/* Reserve minimum height to prevent layout shift */}
                {(debouncedQuery || selectedCategories.length > 0 || selectedTools.length > 0 || selectedTags.length > 0 || selectedModels.length > 0) && (
                  <div className="space-y-3 w-full">
                    <div className="flex items-center justify-between w-full">
                      <h3 className="text-sm font-medium text-muted-foreground">Active Filters</h3>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={clearAllFilters}
                        className="text-xs text-muted-foreground hover:text-foreground h-auto p-1"
                      >
                        Clear all
                      </Button>
                    </div>
                    <div className="flex flex-wrap gap-2 w-full">
                      {/* Search Query Badge */}
                      {debouncedQuery && (
                        <Badge
                          key="search-query"
                          variant="secondary"
                          className="gap-2 py-1.5 px-3 bg-accent-green/10 text-accent-green border-accent-green/20 hover:bg-accent-green/20 transition-colors"
                        >
                          <SearchIcon className="h-3 w-3" />
                          <span className="max-w-[120px] truncate">"{debouncedQuery}"</span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="h-4 w-4 p-0 hover:bg-accent-green/30 rounded-full"
                            onClick={()=>{setSearchInput(""); setDebouncedQuery("");}}
                          >
                            <X className="h-3 w-3"/>
                            <span className="sr-only">Remove search</span>
                          </Button>
                        </Badge>
                      )}

                      {/* Category Badges */}
                      {selectedCategories.map((slug)=>(
                        <Badge
                          key={`cat-${slug}`}
                          variant="secondary"
                          className="gap-2 py-1.5 px-3 hover:bg-muted transition-colors"
                        >
                          <span className="text-xs text-muted-foreground">Category:</span>
                          <span className="max-w-[100px] truncate font-medium">
                            {allCategories.find(c=>c.slug===slug)?.name||slug}
                          </span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="h-4 w-4 p-0 hover:bg-destructive/20 rounded-full"
                            onClick={()=>setSelectedCategories(prev => prev.filter(s => s !== slug))}
                          >
                            <X className="h-3 w-3"/>
                            <span className="sr-only">Remove category</span>
                          </Button>
                        </Badge>
                      ))}

                      {/* Tool Badges */}
                      {selectedTools.map((slug)=>(
                        <Badge
                          key={`tool-${slug}`}
                          variant="secondary"
                          className="gap-2 py-1.5 px-3 hover:bg-muted transition-colors"
                        >
                          <span className="text-xs text-muted-foreground">Tool:</span>
                          <span className="max-w-[100px] truncate font-medium">
                            {allTools.find(t=>t.slug===slug)?.name||slug}
                          </span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="h-4 w-4 p-0 hover:bg-destructive/20 rounded-full"
                            onClick={()=>setSelectedTools(prev => prev.filter(s => s !== slug))}
                          >
                            <X className="h-3 w-3"/>
                            <span className="sr-only">Remove tool</span>
                          </Button>
                        </Badge>
                      ))}

                      {/* Tag Badges */}
                      {selectedTags.map((slug)=>(
                        <Badge
                          key={`tag-${slug}`}
                          variant="secondary"
                          className="gap-2 py-1.5 px-3 hover:bg-muted transition-colors"
                        >
                          <span className="text-xs text-muted-foreground">Tag:</span>
                          <span className="max-w-[100px] truncate font-medium">
                            {allTags.find(t=>t.slug===slug)?.name||slug}
                          </span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="h-4 w-4 p-0 hover:bg-destructive/20 rounded-full"
                            onClick={()=>setSelectedTags(prev => prev.filter(s => s !== slug))}
                          >
                            <X className="h-3 w-3"/>
                            <span className="sr-only">Remove tag</span>
                          </Button>
                        </Badge>
                      ))}

                      {/* Model Badges */}
                      {selectedModels.map((slug)=>(
                        <Badge
                          key={`model-${slug}`}
                          variant="secondary"
                          className="gap-2 py-1.5 px-3 hover:bg-muted transition-colors"
                        >
                          <span className="text-xs text-muted-foreground">Model:</span>
                          <span className="max-w-[100px] truncate font-medium">{slug}</span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="h-4 w-4 p-0 hover:bg-destructive/20 rounded-full"
                            onClick={()=>setSelectedModels(prev => prev.filter(s => s !== slug))}
                          >
                            <X className="h-3 w-3"/>
                            <span className="sr-only">Remove model</span>
                          </Button>
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

           {/* Loading state */}
           {isLoading && finalDisplayedPrompts.length === 0 && (
             <div className="w-full flex justify-center py-12">
               <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
             </div>
           )}

           {/* Prompt List/Grid */}
           {!isLoading || finalDisplayedPrompts.length > 0 ? (
             <>
               {/* Grid or List view with Consistent Width */}
               <div className="w-full">
                 {viewMode === "grid" ? (
                   <div className="grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-3 w-full">
                     {finalDisplayedPrompts.map((prompt, index) => (
                        index === finalDisplayedPrompts.length - 1 ? ( // Check against final list length
                         <div key={prompt.id} ref={lastPromptRef}><PromptCardComponent prompt={prompt} placeholderType="icon" maxTags={1} isSaved={prompt.isSaved} /></div>
                       ) : (
                         <PromptCardComponent key={prompt.id} prompt={prompt} placeholderType="icon" maxTags={1} isSaved={prompt.isSaved} />
                       )
                     ))}
                   </div>
                 ) : (
                   <div className="w-full space-y-4">
                     {finalDisplayedPrompts.map((prompt, index) => (
                        index === finalDisplayedPrompts.length - 1 ? ( // Check against final list length
                          <div key={prompt.id} ref={lastPromptRef} className="w-full">
                            <PromptListItemComponent
                              prompt={{ ...prompt, imageUrl: prompt.imageUrl || '' }}
                              isSaved={prompt.isSaved} />
                          </div>
                       ) : (
                          <div key={prompt.id} className="w-full">
                            <PromptListItemComponent
                              prompt={{ ...prompt, imageUrl: prompt.imageUrl || '' }}
                              isSaved={prompt.isSaved} />
                          </div>
                       )
                     ))}
                   </div>
                 )}
               </div>

               {/* Loading spinner for infinite scroll */}
               {isLoading && finalDisplayedPrompts.length > 0 && (
                 <div className="w-full flex justify-center py-8">
                   <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                 </div>
               )}

               {/* End of content message */}
               {!isLoading && !hasMore && finalDisplayedPrompts.length > 0 && (
                 <div className="w-full mt-8 text-center text-muted-foreground py-6">
                   <p>You've reached the end</p>
                 </div>
               )}

               {/* Enhanced No Results Message */}
                {!isLoading && finalDisplayedPrompts.length === 0 && (
                  <div className="w-full">
                    <div className="mt-8 text-center py-16 border rounded-xl bg-card/50 flex flex-col items-center max-w-md mx-auto">
                      <div className="w-16 h-16 rounded-full bg-muted/50 flex items-center justify-center mb-6">
                        <SearchIcon className="h-8 w-8 text-muted-foreground" />
                      </div>
                      <h3 className="text-xl font-semibold mb-3">No prompts found</h3>
                      <p className="text-muted-foreground mb-6 px-4 leading-relaxed">
                        We couldn't find any prompts matching your criteria. Try adjusting your search or filters.
                      </p>

                      {/* Helpful suggestions */}
                      <div className="space-y-3 mb-6 text-sm text-muted-foreground">
                        <p className="font-medium">Try:</p>
                        <ul className="space-y-1 text-left">
                          <li>• Using broader search terms</li>
                          <li>• Removing some filters</li>
                          <li>• Checking for typos</li>
                          <li>• Browsing popular categories</li>
                        </ul>
                      </div>

                      <div className="flex gap-3">
                        <Button variant="outline" onClick={clearAllFilters}>
                          Clear All Filters
                        </Button>
                        <Button variant="default" onClick={() => window.location.href = '/explore'}>
                          Browse Categories
                        </Button>
                      </div>
                    </div>
                  </div>
                )}

             </>
           ) : (
             // Initial loading state (before any prompts are loaded)
             <div className="w-full flex justify-center py-12">
               <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
             </div>
           )}
          </main>
        </div>
      </div>
    </TooltipProvider>
  )
}

// Badge Component (Imported or defined if needed)
interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "secondary" | "destructive" | "outline"
}

const Badge = ({ className, variant, ...props }: BadgeProps) => {
  // Implement Badge based on ui/badge.tsx
  const baseClass = "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2";
  // Simplified variant mapping for example
  const variantClass = variant === 'secondary' ? 'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80' : 'border-transparent bg-primary text-primary-foreground hover:bg-primary/80';
  return <div className={cn(baseClass, variantClass, className)} {...props} />;
};
