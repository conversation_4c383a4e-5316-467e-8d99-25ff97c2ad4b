/**
 * Test file for CollectionsDropdown component
 * This validates the new bookmark click behavior implementation
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import CollectionsDropdown from '../collections-dropdown';

// Mock the API services
vi.mock('@/lib/api-services', () => ({
  getUserCollectionsForDialog: vi.fn(),
  getPromptCollectionMembership: vi.fn(),
  createCollection: vi.fn(),
}));

vi.mock('@/lib/api-services/updatePromptCollections', () => ({
  updatePromptCollections: vi.fn(),
}));

// Mock Supabase
vi.mock('@supabase/ssr', () => ({
  createBrowserClient: vi.fn(() => ({
    auth: {
      getSession: vi.fn(() => Promise.resolve({
        data: {
          session: {
            user: { id: 'test-user-id' }
          }
        }
      }))
    }
  }))
}));

// Mock sonner toast
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  }
}));

const mockCollections = [
  {
    id: 'collection-1',
    name: 'My Saved Prompts',
    isPublic: false,
    isDefault: true,
    userId: 'test-user-id',
    description: '',
    icon: '',
    promptCount: 5,
    viewCount: 0,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 'collection-2',
    name: 'Work Projects',
    isPublic: true,
    isDefault: false,
    userId: 'test-user-id',
    description: '',
    icon: '',
    promptCount: 3,
    viewCount: 0,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }
];

describe('CollectionsDropdown', () => {
  const defaultProps = {
    isOpen: true,
    onOpenChange: vi.fn(),
    promptId: 'test-prompt-id',
    promptTitle: 'Test Prompt',
    onSuccess: vi.fn(),
    children: <button>Bookmark</button>
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup default API responses
    const { getUserCollectionsForDialog, getPromptCollectionMembership } = require('@/lib/api-services');
    getUserCollectionsForDialog.mockResolvedValue(mockCollections);
    getPromptCollectionMembership.mockResolvedValue({ collectionIds: ['collection-1'] });
  });

  it('renders the dropdown trigger', () => {
    render(<CollectionsDropdown {...defaultProps} />);
    expect(screen.getByText('Bookmark')).toBeInTheDocument();
  });

  it('shows loading state when fetching collections', async () => {
    render(<CollectionsDropdown {...defaultProps} />);
    
    // Should show loading spinner initially
    expect(screen.getByRole('status')).toBeInTheDocument();
  });

  it('displays collections after loading', async () => {
    render(<CollectionsDropdown {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('My Saved Prompts')).toBeInTheDocument();
      expect(screen.getByText('Work Projects')).toBeInTheDocument();
    });
  });

  it('shows checked state for collections that contain the prompt', async () => {
    render(<CollectionsDropdown {...defaultProps} />);
    
    await waitFor(() => {
      const savedPromptsCheckbox = screen.getByRole('checkbox', { name: /my saved prompts/i });
      expect(savedPromptsCheckbox).toBeChecked();
      
      const workProjectsCheckbox = screen.getByRole('checkbox', { name: /work projects/i });
      expect(workProjectsCheckbox).not.toBeChecked();
    });
  });

  it('shows create new collection form when button is clicked', async () => {
    render(<CollectionsDropdown {...defaultProps} />);
    
    await waitFor(() => {
      const createButton = screen.getByText('Create new collection');
      fireEvent.click(createButton);
    });
    
    expect(screen.getByPlaceholderText('Collection name...')).toBeInTheDocument();
    expect(screen.getByText('Create')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });

  it('handles collection toggle correctly', async () => {
    const { updatePromptCollections } = require('@/lib/api-services/updatePromptCollections');
    updatePromptCollections.mockResolvedValue({ success: true });
    
    render(<CollectionsDropdown {...defaultProps} />);
    
    await waitFor(() => {
      const workProjectsCheckbox = screen.getByRole('checkbox', { name: /work projects/i });
      fireEvent.click(workProjectsCheckbox);
    });
    
    expect(updatePromptCollections).toHaveBeenCalledWith(
      'test-user-id',
      'test-prompt-id',
      {
        addToCollectionIds: ['collection-2'],
        removeFromCollectionIds: []
      }
    );
  });

  it('calls onSuccess when collection is toggled successfully', async () => {
    const { updatePromptCollections } = require('@/lib/api-services/updatePromptCollections');
    updatePromptCollections.mockResolvedValue({ success: true });
    
    const onSuccess = vi.fn();
    render(<CollectionsDropdown {...defaultProps} onSuccess={onSuccess} />);
    
    await waitFor(() => {
      const workProjectsCheckbox = screen.getByRole('checkbox', { name: /work projects/i });
      fireEvent.click(workProjectsCheckbox);
    });
    
    await waitFor(() => {
      expect(onSuccess).toHaveBeenCalledWith(true); // Should be true since prompt is now in 2 collections
    });
  });

  it('handles errors gracefully', async () => {
    const { getUserCollectionsForDialog } = require('@/lib/api-services');
    getUserCollectionsForDialog.mockRejectedValue(new Error('Network error'));
    
    render(<CollectionsDropdown {...defaultProps} />);
    
    // Should not crash and should show error handling
    await waitFor(() => {
      // The component should handle the error gracefully
      expect(screen.queryByText('Failed to load collections')).not.toBeInTheDocument();
    });
  });

  it('implements caching correctly', async () => {
    const { getUserCollectionsForDialog } = require('@/lib/api-services');
    
    // First render
    const { rerender } = render(<CollectionsDropdown {...defaultProps} />);
    
    await waitFor(() => {
      expect(getUserCollectionsForDialog).toHaveBeenCalledTimes(1);
    });
    
    // Close and reopen quickly (within cache TTL)
    rerender(<CollectionsDropdown {...defaultProps} isOpen={false} />);
    rerender(<CollectionsDropdown {...defaultProps} isOpen={true} />);
    
    // Should use cache, not call API again
    expect(getUserCollectionsForDialog).toHaveBeenCalledTimes(1);
  });
});
