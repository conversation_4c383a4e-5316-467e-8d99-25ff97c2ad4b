"use client"

import { useState, useRef, use<PERSON><PERSON>back, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import PromptCard from "@/components/prompt-card"
import { Loader2, ChevronDown, Check } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { getPromptsServerAction } from "@/lib/server-actions"
import type { PromptCard as PromptCardType } from "@/lib/types"
import { logPromptShortIdIssues } from "@/lib/utils/debug-helpers"
import { useUser } from "@/lib/hooks/use-user"
import { toast } from "@/components/ui/use-toast"

// Define the sort options type
type SortOption = "newest" | "popular" | "trending"

interface FeaturedPromptsOptimizedProps {
  initialData: {
    popular: PromptCardType[];
    newest: PromptCardType[];
    trending: PromptCardType[];
  };
}

export default function FeaturedPromptsOptimized({ initialData }: FeaturedPromptsOptimizedProps) {
  const { user, isLoading: isUserLoading } = useUser();

  const PROMPTS_PER_PAGE = 6
  const [hasRefreshedSavedStatus, setHasRefreshedSavedStatus] = useState(false)
  
  // Create a unique instance ID for debugging
  const instanceId = useRef(Math.random().toString(36).substr(2, 9))
  const loadingRef = useRef(false)
  
  // Log initial data to check for duplicates
  console.log('[FeaturedPromptsOptimized] Initial data received:', {
    instanceId: instanceId.current,
    popular: initialData.popular.length,
    newest: initialData.newest.length,
    trending: initialData.trending.length,
    popularIds: initialData.popular.map(p => p.id),
    newestIds: initialData.newest.map(p => p.id),
    trendingIds: initialData.trending.map(p => p.id),
  })
  
  // Helper function to deduplicate prompts by ID
  const deduplicatePrompts = (prompts: PromptCardType[]): PromptCardType[] => {
    const seen = new Set<string>()
    const duplicates: string[] = []
    const result = prompts.filter(prompt => {
      if (seen.has(prompt.id)) {
        duplicates.push(prompt.id)
        return false
      }
      seen.add(prompt.id)
      return true
    })
    
    if (duplicates.length > 0) {
      console.warn(`[FeaturedPromptsOptimized] Found ${duplicates.length} duplicate prompts:`, duplicates)
    }
    
    return result
  }
  
  // State for displayed prompts - initialize with pre-fetched data (deduplicated)
  const [displayedPrompts, setDisplayedPrompts] = useState<PromptCardType[]>(
    deduplicatePrompts(initialData.popular)
  )
  const [isLoading, setIsLoading] = useState(false)
  const [hasMore, setHasMore] = useState(initialData.popular.length >= PROMPTS_PER_PAGE)
  const [page, setPage] = useState(1)

  // Sort options state
  const [sortOption, setSortOption] = useState<SortOption>("popular")

  // Helper function to get sort parameters for the API
  const getSortByOption = (option: SortOption): { field: string; order: "asc" | "desc" } => {
    switch (option) {
      case "newest":
        return { field: "created_at", order: "desc" }
      case "popular":
        return { field: "rating", order: "desc" }
      case "trending":
        return { field: "trending_score", order: "desc" }
      default:
        return { field: "created_at", order: "desc" }
    }
  }

  // Function to load more prompts
  const loadMorePrompts = useCallback(async () => {
    if (isLoading || loadingRef.current) {
      console.log(`[FeaturedPromptsOptimized-${instanceId.current}] Load more skipped - already loading`)
      return
    }

    loadingRef.current = true
    setIsLoading(true)
    
    console.log(`[FeaturedPromptsOptimized-${instanceId.current}] Starting to load more prompts, page ${page}`)

    try {
      // Get sorted prompts from the server action
      const sortBy = getSortByOption(sortOption)
      const offset = page * PROMPTS_PER_PAGE

      const newPrompts = await getPromptsServerAction({
        limit: PROMPTS_PER_PAGE,
        offset: offset,
        sortBy: sortBy.field,
        sortOrder: sortBy.order,
        currentUserId: user?.id,
      })

      console.log(`[FeaturedPromptsOptimized-${instanceId.current}] Loaded ${newPrompts.length} additional prompts, page ${page}`)

      // Check for missing shortIds
      logPromptShortIdIssues(newPrompts, "FeaturedPromptsOptimized-loadMore")

      // Deduplicate prompts before adding them to prevent duplicate keys
      setDisplayedPrompts((prevPrompts) => {
        const existingIds = new Set(prevPrompts.map(p => p.id))
        const uniqueNewPrompts = newPrompts.filter(p => !existingIds.has(p.id))
        console.log(`[FeaturedPromptsOptimized-${instanceId.current}] Adding ${uniqueNewPrompts.length} unique prompts (filtered ${newPrompts.length - uniqueNewPrompts.length} duplicates)`)
        return [...prevPrompts, ...uniqueNewPrompts]
      })
      setPage((prevPage) => prevPage + 1)

      // Assume there are more prompts if we got a full page
      setHasMore(newPrompts.length === PROMPTS_PER_PAGE)
    } catch (error) {
      console.error(`[FeaturedPromptsOptimized-${instanceId.current}] Error loading more prompts:`, error)
      setHasMore(false)
    } finally {
      loadingRef.current = false
      setIsLoading(false)
    }
  }, [isLoading, sortOption, page, user?.id])

  // Reference for the intersection observer
  const observerRef = useRef<IntersectionObserver | null>(null)
  
  // Cleanup effect for IntersectionObserver
  useEffect(() => {
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [])

  const lastPromptRef = useCallback(
    (node: HTMLDivElement | null) => {
      if (isLoading) return

      if (observerRef.current) observerRef.current.disconnect()

      observerRef.current = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting && hasMore) {
          loadMorePrompts()
        }
      })

      if (node) observerRef.current.observe(node)
    },
    [isLoading, hasMore, loadMorePrompts],
  )

  // Function to refresh saved status for initial prompts when user loads
  const refreshSavedStatus = useCallback(async () => {
    if (!user?.id || hasRefreshedSavedStatus) return;

    console.log(`[FeaturedPromptsOptimized-${instanceId.current}] Refreshing saved status for user:`, user.id);

    try {
      // Get the current sort option's data with user context
      const sortBy = getSortByOption(sortOption);
      const refreshedPrompts = await getPromptsServerAction({
        limit: PROMPTS_PER_PAGE,
        offset: 0,
        sortBy: sortBy.field,
        sortOrder: sortBy.order,
        currentUserId: user.id,
      });

      console.log(`[FeaturedPromptsOptimized-${instanceId.current}] Refreshed ${refreshedPrompts.length} prompts with saved status`);

      // Update displayed prompts with correct saved status
      setDisplayedPrompts(deduplicatePrompts(refreshedPrompts));
      setHasRefreshedSavedStatus(true);
    } catch (error) {
      console.error(`[FeaturedPromptsOptimized-${instanceId.current}] Error refreshing saved status:`, error);
    }
  }, [user?.id, hasRefreshedSavedStatus, sortOption]);

  // Refresh saved status when user loads
  useEffect(() => {
    if (!isUserLoading && user?.id && !hasRefreshedSavedStatus) {
      refreshSavedStatus();
    }
  }, [isUserLoading, user?.id, hasRefreshedSavedStatus, refreshSavedStatus]);

  // Function to handle sort option change - use pre-fetched data for instant switching
  const handleSortChange = (option: SortOption) => {
    console.log(`[FeaturedPromptsOptimized-${instanceId.current}] Changing sort from ${sortOption} to ${option}`)

    // Reset loading state
    loadingRef.current = false
    setIsLoading(false)

    setSortOption(option)

    // Reset the refresh flag when changing sort options
    setHasRefreshedSavedStatus(false)

    // Use pre-fetched data for instant switching
    let newPrompts: PromptCardType[] = []
    switch (option) {
      case "popular":
        newPrompts = initialData.popular
        break
      case "newest":
        newPrompts = initialData.newest
        break
      case "trending":
        newPrompts = initialData.trending
        break
    }

    const deduplicatedPrompts = deduplicatePrompts(newPrompts)
    console.log(`[FeaturedPromptsOptimized-${instanceId.current}] Setting ${deduplicatedPrompts.length} prompts for ${option}`)

    setDisplayedPrompts(deduplicatedPrompts)
    setPage(1)
    setHasMore(newPrompts.length >= PROMPTS_PER_PAGE)

    // Refresh saved status for the new sort option if user is loaded
    if (user?.id) {
      setTimeout(() => refreshSavedStatus(), 100); // Small delay to ensure state is updated
    }
  }

  // Function to get the display name for a sort option
  const getSortOptionDisplayName = (option: SortOption): string => {
    switch (option) {
      case "newest":
        return "Newest"
      case "popular":
        return "Popular"
      case "trending":
        return "Trending"
      default:
        return "Popular"
    }
  }



  return (
    <div className="space-y-6">
      {/* Header with Sort Options */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Featured Prompts</h2>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="flex items-center gap-2">
              {getSortOptionDisplayName(sortOption)}
              <ChevronDown className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => handleSortChange("popular")}>
              <div className="flex items-center gap-2">
                {sortOption === "popular" && <Check className="h-4 w-4" />}
                Popular
              </div>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleSortChange("newest")}>
              <div className="flex items-center gap-2">
                {sortOption === "newest" && <Check className="h-4 w-4" />}
                Newest
              </div>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleSortChange("trending")}>
              <div className="flex items-center gap-2">
                {sortOption === "trending" && <Check className="h-4 w-4" />}
                Trending
              </div>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Prompts Grid */}
      <div key={`grid-${sortOption}`} className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {displayedPrompts.map((prompt, index) => (
          <div
            key={`${sortOption}-${prompt.id}`}
            ref={index === displayedPrompts.length - 1 ? lastPromptRef : null}
          >
            <PromptCard
              prompt={prompt}
              isSaved={prompt.isSaved}
            />
          </div>
        ))}
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      )}

      {/* Load More Button (fallback if intersection observer doesn't work) */}
      {!isLoading && hasMore && displayedPrompts.length >= PROMPTS_PER_PAGE && (
        <div className="flex justify-center">
          <Button onClick={loadMorePrompts} variant="outline">
            Load More Prompts
          </Button>
        </div>
      )}

      {/* No More Prompts Message */}
      {!hasMore && displayedPrompts.length > 0 && (
        <div className="text-center text-muted-foreground py-8">
          You've reached the end of the prompts!
        </div>
      )}

      {/* Empty State */}
      {displayedPrompts.length === 0 && !isLoading && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">No prompts found.</p>
        </div>
      )}
    </div>
  )
}